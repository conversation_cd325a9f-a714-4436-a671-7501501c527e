<script setup lang="ts">
import type { EasyCurdConfig } from '@billing/curd'
import type { QuotaPackage } from '@/api/quota'
import { EasyCurd } from '@billing/curd'
import { Avatar, AvatarFallback, AvatarImage, Badge, Button, Card, CardContent, CardHeader, CardTitle, Progress, Tabs, TabsContent, TabsList, TabsTrigger } from '@billing/ui'
import { http } from '@uozi-admin/request'
import { Activity, AlertTriangle, CheckCircle, Package, Shield, ShieldOff, TrendingUp, XCircle } from 'lucide-vue-next'
import { computed, onMounted, ref } from 'vue'
import { keyApi, searchUsers } from '@/api'
import { quotaApi } from '@/api/quota'

// 状态定义
const activeTab = ref('list')

// 统计数据
const stats = ref({
  total: 0,
  active: 0,
  expired: 0,
  exhausted: 0,
  totalCost: 0, // 总成本（不同模块可以相加）
  totalRequests: 0, // 总请求数（不同模块可以相加）
  expiringSoon: 0,
})

const filteredConfig: EasyCurdConfig<QuotaPackage> = {
  api: quotaApi,
  title: '资源包管理',
  primaryKey: 'id',

  // 表单字段配置
  formFields: [
    {
      key: 'user_id',
      label: '关联用户',
      type: 'combobox',
      required: true,
      placeholder: '请输入用户名或邮箱搜索',
      remoteSearch: {
        searchApi: query => searchUsers(query),
        debounceMs: 300,
        minSearchLength: 1,
        showDefaultOptions: false,
        renderOption: (option) => {
          const parts = [option.label]
          if (option.email) {
            parts.push(`(${option.email})`)
          }
          return parts.join(' ')
        },
      },
    },
    {
      key: 'module',
      label: '服务类型',
      type: 'select',
      required: true,
      options: [
        { label: 'LLM服务', value: 'llm' },
        { label: 'TTS服务', value: 'tts' },
        { label: 'ASR服务', value: 'asr' },
      ],
    },
    {
      key: 'quota',
      label: '配额数量',
      type: 'number',
      required: true,
      rules: [
        { required: true, message: '配额数量是必填项' },
        { min: 1, message: '配额数量必须大于0' },
      ],
    },
    {
      key: 'api_key',
      label: 'API Key',
      type: 'select',
      required: true,
      linkage: {
        dependsOn: ['user_id'],
        handler: async ({ user_id }) => {
          if (!user_id)
            return []
          const res = await keyApi.getList({ user_id })
          return res.data.map(item => ({
            label: `${item.name}(${item.api_key})`,
            value: item.api_key,
          }))
        },
      },
      placeholder: '留空表示通用资源包',
    },
    {
      key: 'model_name',
      label: '模型名称',
      type: 'text',
      placeholder: '留空表示通用模型',
    },
    {
      key: 'expires_at',
      label: '过期时间',
      type: 'datetime',
    },
    {
      key: 'type',
      label: '资源包类型',
      type: 'select',
      defaultValue: 'admin',
      options: [
        { label: '管理员赠送', value: 'admin' },
        { label: '购买获得', value: 'purchase' },
        { label: '促销活动', value: 'promotion' },
      ],
    },
    {
      key: 'description',
      label: '描述',
      type: 'textarea',
      placeholder: '请输入资源包描述...',
    },
  ],

  // 功能开关
  features: {
    create: true,
    edit: true,
    delete: false,
    batchDelete: false,
    search: true,
  },

  // 消息配置
  successMessages: {
    create: '资源包创建成功！',
    update: '资源包更新成功！',
  },
}

// 模块统计 - 按不同模块分开统计，不能简单相加因为单位不同
const moduleStats = computed(() => {
  return [
    {
      module: 'llm',
      name: 'LLM服务',
      count: 45,
      total_usage: 8200000,
      quota: 15500000,
      unit: 'tokens',
      cost: 1520.50,
    },
    {
      module: 'tts',
      name: 'TTS服务',
      count: 28,
      total_usage: 1800000,
      quota: 3200000,
      unit: 'characters',
      cost: 380.20,
    },
    {
      module: 'asr',
      name: 'ASR服务',
      count: 15,
      total_usage: 900,
      quota: 1800,
      unit: 'seconds',
      cost: 180.30,
    },
  ]
})

// 获取统计数据
async function fetchStats() {
  try {
    // 这里应该调用实际的API获取统计数据
    // const response = await quotaApi.getStats()
    // stats.value = response

    // 模拟数据
    stats.value = {
      total: 128,
      active: 98,
      expired: 15,
      exhausted: 12,
      totalCost: 2081.00, // 总成本（不同模块可以相加）
      totalRequests: 88, // 总请求数（不同模块可以相加）
      expiringSoon: 8,
    }
  }
  catch (error) {
    console.error('获取统计数据失败:', error)
  }
}

// 格式化配额
function formatQuota(quota: number) {
  if (quota >= 1000000) {
    return `${(quota / 1000000).toFixed(1)}M`
  }
  if (quota >= 1000) {
    return `${(quota / 1000).toFixed(1)}K`
  }
  return quota.toLocaleString()
}

// 格式化不同单位的使用量
function formatUsage(usage: number, unit: string) {
  if (unit === 'tokens' || unit === 'characters') {
    // 对于tokens和characters，使用K/M格式
    if (usage >= 1000000) {
      return `${(usage / 1000000).toFixed(1)}M ${unit}`
    }
    if (usage >= 1000) {
      return `${(usage / 1000).toFixed(1)}K ${unit}`
    }
    return `${usage.toLocaleString()} ${unit}`
  }
  else if (unit === 'seconds') {
    // 对于秒数，格式化为时分秒
    if (usage >= 3600) {
      const hours = Math.floor(usage / 3600)
      const minutes = Math.floor((usage % 3600) / 60)
      const seconds = usage % 60
      return `${hours}h${minutes}m${seconds}s`
    }
    else if (usage >= 60) {
      const minutes = Math.floor(usage / 60)
      const seconds = usage % 60
      return `${minutes}m${seconds}s`
    }
    else {
      return `${usage}s`
    }
  }
  else {
    // 其他单位直接显示
    return `${usage.toLocaleString()} ${unit}`
  }
}

// 格式化时间
function formatDate(timestamp: number | string) {
  if (!timestamp)
    return '-'
  const date = typeof timestamp === 'number' ? new Date(timestamp) : new Date(timestamp)
  return new Intl.DateTimeFormat('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit',
  }).format(date)
}

// 获取模块名称
function getModuleName(module: string) {
  const moduleNames = {
    llm: 'LLM',
    tts: 'TTS',
    asr: 'ASR',
  }
  return moduleNames[module as keyof typeof moduleNames] || module
}

// 获取类型标签
function getTypeLabel(type: string) {
  const types = {
    admin: '管理员赠送',
    purchase: '购买获得',
    promotion: '促销活动',
  }
  return types[type as keyof typeof types] || type
}

// 获取状态颜色
function getStatusVariant(status: string) {
  switch (status) {
    case 'active':
      return 'default'
    case 'expired':
      return 'secondary'
    case 'exhausted':
      return 'outline'
    case 'disabled':
      return 'destructive'
    default:
      return 'secondary'
  }
}

// 获取状态文本
function getStatusText(status: string) {
  const statusMap = {
    active: '生效',
    expired: '过期',
    exhausted: '用完',
    disabled: '禁用',
  }
  return statusMap[status as keyof typeof statusMap] || status
}

// 计算使用率
function getUsagePercentage(used: number, quota: number) {
  if (quota === 0)
    return 0
  return Math.min((used / quota) * 100, 100).toFixed(2)
}

// 禁用资源包
async function disableQuotaPackage(id: string) {
  try {
    await http.post(`/admin/billing/quota_packages/${id}/disable`)
    await fetchStats() // 刷新统计数据
  }
  catch (error) {
    console.error('禁用资源包失败:', error)
  }
}

// 启用资源包
async function enableQuotaPackage(id: string) {
  try {
    await http.post(`/admin/billing/quota_packages/${id}/enable`)
    await fetchStats() // 刷新统计数据
  }
  catch (error) {
    console.error('启用资源包失败:', error)
  }
}

// 导出数据
function exportData() {
  // 实现导出逻辑
  console.log('导出资源包数据')
}

// 初始化
onMounted(() => {
  fetchStats()
})
</script>

<template>
  <div class="space-y-6">
    <!-- 页面标题 -->
    <div class="flex items-center justify-between">
      <div>
        <h1 class="text-2xl font-semibold text-gray-900">
          资源包管理
        </h1>
        <p class="mt-2 text-sm text-gray-700">
          管理用户资源包，包括LLM、TTS、ASR等服务的配额分配。支持为指定Key或全局分配。
        </p>
      </div>
    </div>

    <!-- 标签页导航 -->
    <Tabs
      v-model="activeTab"
      class="w-full"
    >
      <TabsList class="grid w-full grid-cols-3 mb-2">
        <TabsTrigger value="overview">
          概览
        </TabsTrigger>
        <TabsTrigger value="list">
          资源包列表
        </TabsTrigger>
        <TabsTrigger value="analytics">
          统计分析
        </TabsTrigger>
      </TabsList>

      <!-- 概览标签页 -->
      <TabsContent
        value="overview"
        class="space-y-6"
      >
        <!-- 总体统计卡片 -->
        <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
          <Card>
            <CardHeader class="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle class="text-sm font-medium">
                总资源包
              </CardTitle>
              <Package class="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div class="text-2xl font-bold">
                {{ stats.total }}
              </div>
              <p class="text-xs text-muted-foreground">
                活跃: {{ stats.active }}，过期: {{ stats.expired }}
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader class="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle class="text-sm font-medium">
                总成本
              </CardTitle>
              <TrendingUp class="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div class="text-2xl font-bold">
                ¥{{ stats.totalCost.toFixed(2) }}
              </div>
              <p class="text-xs text-muted-foreground">
                所有模块用量总成本
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader class="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle class="text-sm font-medium">
                总请求数
              </CardTitle>
              <Activity class="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div class="text-2xl font-bold">
                {{ stats.totalRequests.toLocaleString() }}
              </div>
              <p class="text-xs text-muted-foreground">
                所有模块总请求次数
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader class="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle class="text-sm font-medium">
                即将过期
              </CardTitle>
              <AlertTriangle class="h-4 w-4 text-yellow-500" />
            </CardHeader>
            <CardContent>
              <div class="text-2xl font-bold text-yellow-600">
                {{ stats.expiringSoon }}
              </div>
              <p class="text-xs text-muted-foreground">
                7天内过期
              </p>
            </CardContent>
          </Card>
        </div>

        <!-- 按模块统计 -->
        <Card>
          <CardHeader>
            <CardTitle>按服务类型统计</CardTitle>
          </CardHeader>
          <CardContent>
            <div class="space-y-4">
              <div
                v-for="module in moduleStats"
                :key="module.module"
                class="flex items-center justify-between p-4 border rounded-lg"
              >
                <div class="flex items-center gap-3">
                  <Badge variant="outline">
                    {{ module.name }}
                  </Badge>
                  <div class="text-sm text-gray-600">
                    {{ module.count }} 个资源包
                  </div>
                </div>
                <div class="text-right">
                  <div class="font-medium">
                    {{ formatUsage(module.total_usage, module.unit) }} / {{ formatUsage(module.quota, module.unit) }}
                  </div>
                  <div class="text-sm text-gray-500">
                    使用率: {{ Math.round((module.total_usage / module.quota) * 100) }}%
                  </div>
                  <div class="text-xs text-gray-400">
                    成本: ¥{{ module.cost.toFixed(2) }}
                  </div>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      </TabsContent>

      <!-- 资源包列表标签页 -->
      <TabsContent
        value="list"
        class="space-y-6"
      >
        <!-- 使用 EasyCurd 组件 -->
        <EasyCurd :config="filteredConfig">
          <!-- 自定义用户列渲染 -->
          <template #cell-user_id="{ row }">
            <div
              v-if="row.user"
              class="flex items-center"
            >
              <Avatar class="h-8 w-8 mr-3">
                <AvatarImage :src="row.user.avatar" />
                <AvatarFallback>{{ (row.user.name || '').charAt(0).toUpperCase() }}</AvatarFallback>
              </Avatar>
              <div>
                <div class="text-sm font-medium text-gray-900">
                  {{ row.user.name }}
                </div>
                <div class="text-sm text-gray-500">
                  {{ row.user.email }}
                </div>
              </div>
            </div>
            <div
              v-else
              class="text-sm text-gray-400"
            >
              用户信息缺失
            </div>
          </template>

          <!-- 自定义模块列渲染 -->
          <template #cell-module="{ value }">
            <Badge variant="outline">
              {{ getModuleName(value) }}
            </Badge>
          </template>

          <!-- 自定义配额使用列渲染 -->
          <template #cell-quota="{ row }">
            <div class="space-y-2">
              <div class="flex justify-between text-sm">
                <span>{{ formatQuota(row.used) }} / {{ formatQuota(row.quota) }}</span>
                <span class="text-gray-500">{{ getUsagePercentage(row.used, row.quota) }}%</span>
              </div>
              <Progress
                :value="getUsagePercentage(row.used, row.quota)"
                class="h-2"
              />
              <div class="text-xs text-gray-500">
                剩余: {{ formatQuota(row.available) }}
              </div>
            </div>
          </template>

          <!-- 自定义API Key列渲染 -->
          <template #cell-api_key="{ value }">
            <div class="text-sm font-mono text-gray-600">
              {{ value ? `${value.substring(0, 8)}****` : '通用' }}
            </div>
          </template>

          <!-- 自定义模型列渲染 -->
          <template #cell-model_name="{ value }">
            <div class="text-sm text-gray-600">
              {{ value || '通用' }}
            </div>
          </template>

          <!-- 自定义过期时间列渲染 -->
          <template #cell-expires_at="{ value }">
            <div class="text-sm text-gray-500">
              {{ value ? formatDate(value) : '永不过期' }}
            </div>
          </template>

          <!-- 自定义类型列渲染 -->
          <template #cell-type="{ value }">
            <Badge variant="outline">
              {{ getTypeLabel(value) }}
            </Badge>
          </template>

          <!-- 自定义状态列渲染 -->
          <template #cell-status="{ value }">
            <Badge :variant="getStatusVariant(value)">
              <CheckCircle
                v-if="value === 'active'"
                class="w-3 h-3 mr-1"
              />
              <XCircle
                v-else-if="value === 'disabled'"
                class="w-3 h-3 mr-1"
              />
              {{ getStatusText(value) }}
            </Badge>
          </template>

          <!-- 自定义操作列 -->
          <template #actions="{ row }">
            <div class="flex items-center justify-end gap-1">
              <Button
                v-if="row.status === 'active'"
                variant="ghost"
                size="sm"
                @click="disableQuotaPackage(row.id)"
              >
                <ShieldOff class="w-4 h-4" />
              </Button>
              <Button
                v-else-if="row.status === 'disabled'"
                variant="ghost"
                size="sm"
                @click="enableQuotaPackage(row.id)"
              >
                <Shield class="w-4 h-4" />
              </Button>
            </div>
          </template>

          <!-- 自定义创建时间列渲染 -->
          <template #cell-created_at="{ value }">
            <div class="text-sm text-gray-500">
              {{ formatDate(value) }}
            </div>
          </template>
        </EasyCurd>
      </TabsContent>

      <!-- 统计分析标签页 -->
      <TabsContent
        value="analytics"
        class="space-y-6"
      >
        <Card>
          <CardHeader>
            <CardTitle>统计分析</CardTitle>
          </CardHeader>
          <CardContent>
            <div class="text-center py-8 text-gray-500">
              <TrendingUp class="w-12 h-12 mx-auto mb-4 text-gray-300" />
              <p>统计分析功能开发中...</p>
              <p class="text-sm">
                将提供用量趋势、用户分析等功能
              </p>
            </div>
          </CardContent>
        </Card>
      </TabsContent>
    </Tabs>
  </div>
</template>
